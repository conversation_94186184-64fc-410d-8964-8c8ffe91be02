import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';
import ResponsiveProductDetailPage from '../../../../components/ResponsiveProductDetailPage';
import { extractProductIdFromSlug, isValidProductSlug } from '../../../../utils/generateSlug';
import { getProductWithDetails, getCategoryById, getSubcategoryById, getProducts } from '../../../../lib/mysql-database';
import { generateProductUrl } from '../../../../utils/generateSlug';
import { ProductJsonLd } from '../../../../components/SEO/ProductSEO';

interface ProductPageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

// دالة لجلب بيانات المنتج مباشرة من قاعدة البيانات (بدون API)
async function fetchProductData(productId: string): Promise<{
  product: ProductWithDetails | null;
  category: Category | null;
  subcategory: Subcategory | null;
}> {
  try {
    console.log('🚀 Server: Fetching product details for ID:', productId);

    // جلب المنتج مباشرة من قاعدة البيانات
    const product = await getProductWithDetails(productId);

    if (!product) {
      console.log(`❌ Server: Product not found for ID: ${productId}`);
      return { product: null, category: null, subcategory: null };
    }

    console.log('✅ Server: Product found, fetching related data...');

    // جلب بيانات الفئة والفئة الفرعية
    const [category, subcategory] = await Promise.all([
      product.category_id ? getCategoryById(product.category_id) : null,
      product.subcategory_id ? getSubcategoryById(product.subcategory_id) : null
    ]);

    console.log('📦 Server: All product data fetched successfully');

    return { product, category, subcategory };
  } catch (error) {
    console.error('❌ Server: Error fetching product details:', error);
    return { product: null, category: null, subcategory: null };
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const slug = resolvedParams?.slug || '';

  // استخراج معرف المنتج من الـ slug
  const productId = extractProductIdFromSlug(slug);
  
  if (!productId) {
    return {
      title: 'منتج غير موجود - DROOB HAJER',
      description: 'المنتج المطلوب غير موجود',
    };
  }

  // جلب بيانات المنتج
  const { product } = await fetchProductData(productId);

  if (!product) {
    return {
      title: 'منتج غير موجود - DROOB HAJER',
      description: 'المنتج المطلوب غير موجود',
    };
  }

  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  // إنشاء عنوان مُحسن (أقل من 60 حرف) بدون اسم الشركة
  const optimizedTitle = productTitle.length > 55
    ? productTitle.substring(0, 52) + '...'
    : productTitle;

  // إنشاء وصف مُحسن (بين 120-160 حرف) غني بالكلمات المفتاحية
  let optimizedDescription = '';
  if (productDescription && productDescription.length > 50) {
    // استخدام الوصف الموجود مع تحسين
    const enhancedDesc = locale === 'ar'
      ? `${productDescription.substring(0, 100)} - معدات فنادق احترافية عالية الجودة للبوفيه والمطاعم من دروب هجر`
      : `${productDescription.substring(0, 100)} - Professional high quality hotel buffet equipment from DROOB HAJER`;

    optimizedDescription = enhancedDesc.length > 160
      ? enhancedDesc.substring(0, 157) + '...'
      : enhancedDesc;
  } else {
    // إنشاء وصف افتراضي غني بالكلمات المفتاحية
    const defaultDesc = locale === 'ar'
      ? `${productTitle} - معدات فنادق احترافية عالية الجودة للبوفيه والمطاعم. أطباق وأواني تقديم مقاومة ومتينة بأفضل الأسعار من دروب هجر`
      : `${productTitle} - Professional high quality hotel buffet equipment. Durable serving dishes and platters at best prices from DROOB HAJER`;

    optimizedDescription = defaultDesc.length > 160
      ? defaultDesc.substring(0, 157) + '...'
      : defaultDesc;
  }

  // إنشاء الرابط الكامل للمنتج
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://droobhajer.com';
  const productUrl = `${baseUrl}/${locale}/products/${slug}`;

  // تحويل روابط الصور المحلية إلى روابط كاملة
  const productImages = product.images && product.images.length > 0
    ? product.images.map(img => ({
        url: img.image_url.startsWith('http') ? img.image_url : `${baseUrl}${img.image_url}`,
        width: 1200,
        height: 630,
        alt: optimizedTitle,
      }))
    : [{
        url: `${baseUrl}/placeholder-image.jpg`,
        width: 1200,
        height: 630,
        alt: optimizedTitle,
      }];

  // كلمات مفتاحية محسنة ومتنوعة
  const categoryKeywords = locale === 'ar'
    ? 'بوفيه, أطباق بوفيه, أدوات بوفيه, معدات فنادق, أواني تقديم, صواني تقديم, أطباق مطاعم, معدات مطابخ تجارية, أدوات ضيافة'
    : 'buffet, buffet plates, buffet equipment, hotel equipment, serving dishes, serving platters, restaurant plates, commercial kitchen equipment, hospitality supplies';

  // كلمات مفتاحية خاصة بالمنتج
  const productSpecificKeywords = locale === 'ar'
    ? `${productTitle}, ${productTitle} فندقي, ${productTitle} للمطاعم, ${productTitle} احترافي`
    : `${productTitle}, hotel ${productTitle.toLowerCase()}, restaurant ${productTitle.toLowerCase()}, professional ${productTitle.toLowerCase()}`;

  // إنشاء Schema Markup للمنتج
  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": productTitle,
    "description": productDescription || optimizedDescription,
    "image": product.images?.map(img => img.image_url) || ['/placeholder-image.jpg'],
    "brand": {
      "@type": "Brand",
      "name": "DROOB HAJER",
      "url": baseUrl
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "DROOB HAJER",
      "url": baseUrl
    },
    "category": "Hotel Equipment",
    "sku": product.id,
    "mpn": product.id,
    "offers": {
      "@type": "Offer",
      "url": productUrl,
      "priceCurrency": "SAR",
      "price": product.price || "0",
      "availability": product.is_available ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
      "seller": {
        "@type": "Organization",
        "name": "DROOB HAJER"
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.5",
      "reviewCount": "10",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  // إضافة المواصفات إذا كانت متوفرة
  if (product.specifications && product.specifications.length > 0) {
    (productSchema as typeof productSchema & { additionalProperty: Array<{ "@type": string; name: string; value: string }> }).additionalProperty = product.specifications.map(spec => ({
      "@type": "PropertyValue",
      "name": locale === 'ar' ? spec.spec_key_ar : spec.spec_key,
      "value": locale === 'ar' ? spec.spec_value_ar : spec.spec_value
    }));
  }

  return {
    title: optimizedTitle,
    description: optimizedDescription,
    keywords: `${productSpecificKeywords}, ${categoryKeywords}`,
    openGraph: {
      title: optimizedTitle,
      description: optimizedDescription,
      url: productUrl,
      siteName: locale === 'ar' ? 'دروب هاجر' : 'DROOB HAJER',
      images: productImages,
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: optimizedTitle,
      description: optimizedDescription,
      images: productImages,
      site: '@droobhajer',
    },
    alternates: {
      canonical: productUrl,
      languages: {
        'ar': `${baseUrl}/ar/products/${slug}`,
        'en': `${baseUrl}/en/products/${slug}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    other: {
      'product:schema': JSON.stringify(productSchema),
    },
  };
}

// دالة لإنشاء static params للمنتجات الشائعة (أول 100 منتج)
export async function generateStaticParams(): Promise<{ locale: string; slug: string }[]> {
  try {
    console.log('🔧 Generating static params for products...');

    // جلب أول 100 منتج لإنشاء static pages
    const products = await getProducts();
    const limitedProducts = products.slice(0, 100); // تحديد العدد لتجنب بناء آلاف الصفحات

    const params: { locale: string; slug: string }[] = [];

    for (const product of limitedProducts) {
      // إنشاء slug للغة العربية
      const arUrl = generateProductUrl(product, 'ar');
      const arSlug = arUrl.replace('/products/', '');

      // إنشاء slug للغة الإنجليزية
      const enUrl = generateProductUrl(product, 'en');
      const enSlug = enUrl.replace('/products/', '');

      params.push(
        { locale: 'ar', slug: arSlug },
        { locale: 'en', slug: enSlug }
      );
    }

    console.log(`✅ Generated ${params.length} static params for products`);
    return params;
  } catch (error) {
    console.error('❌ Error generating static params:', error);
    return []; // إرجاع مصفوفة فارغة في حالة الخطأ
  }
}

export default async function ProductDetailPage({ params }: ProductPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const slug = resolvedParams?.slug || '';

  // التحقق من صحة تنسيق الـ slug
  if (!isValidProductSlug(slug)) {
    console.error('❌ Invalid product slug format:', slug);
    notFound();
  }

  // استخراج معرف المنتج من الـ slug
  const productId = extractProductIdFromSlug(slug);
  
  if (!productId) {
    console.error('❌ Could not extract product ID from slug:', slug);
    notFound();
  }

  // جلب البيانات من الخادم
  const { product, category } = await fetchProductData(productId);

  // إذا لم يتم العثور على المنتج، إظهار صفحة 404
  if (!product) {
    console.error('❌ Product not found for ID:', productId);
    notFound();
  }

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      {/* إضافة Product Schema.org المحسن */}
      <ProductJsonLd
        product={{
          id: parseInt(product.id) || 0,
          name: product.title,
          name_ar: product.title_ar,
          description: product.description,
          description_ar: product.description_ar,
          category: category || undefined,
          subcategory: undefined,
          price: product.price,
          images: product.images,
          specifications: product.specifications && product.specifications.length > 0
            ? product.specifications.map(spec =>
                `${locale === 'ar' ? spec.spec_key_ar : spec.spec_key}: ${locale === 'ar' ? spec.spec_value_ar : spec.spec_value}`
              ).join(', ')
            : undefined,
          sku: `DH-${product.id}`, // إنشاء SKU من معرف المنتج
          is_available: product.is_available
        }}
        locale={locale}
      />

      <ResponsiveProductDetailPage
        locale={locale}
        product={product}
        category={category}
      />
    </div>
  );
}
